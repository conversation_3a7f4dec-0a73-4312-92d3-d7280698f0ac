<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="地区名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入地区名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd()"
          v-hasPermi="['buy:region:add']"
        >新增省份</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="regionList"
      row-key="id"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="地区名称" prop="name" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="层级" align="center" prop="level">
         <template #default="scope">
           <el-tag v-if="scope.row.level === 1">省份</el-tag>
           <el-tag type="success" v-if="scope.row.level === 2">城市</el-tag>
         </template>
      </el-table-column>
      <el-table-column label="城市等级" align="center" prop="tag">
        <template #default="scope">
          <span v-if="scope.row.tag">{{ getTagLabel(scope.row.tag) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:region:edit']">修改</el-button>
          <el-button v-if="scope.row.level === 1" link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['buy:region:add']">新增城市</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:region:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="regionRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级地区" prop="parentId">
           <el-tree-select
              v-model="form.parentId"
              :data="regionOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="不选则为顶级(省份)"
              check-strictly
              clearable
           />
        </el-form-item>
        <el-form-item label="地区名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入地区名称" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder" v-if="form.level === 1">
          <el-input-number v-model="form.sortOrder" placeholder="请输入排序" :min="0" />
        </el-form-item>
        
        <el-form-item label="城市等级" prop="tag" v-if="form.level === 2">
          <el-select v-model="form.tag" placeholder="请选择城市等级" clearable>
            <el-option
              v-for="item in cityTagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Region">
import { listRegion, getRegion, delRegion, addRegion, updateRegion } from "@/api/buy/region";

const { proxy } = getCurrentInstance();

const regionList = ref([]);
const regionOptions = ref([]); // 用于父级选择器的选项
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");

// 城市等级标签选项
const cityTagOptions = [
  { value: 1, label: '一线城市' },
  { value: 2, label: '新一线城市' },
  { value: 3, label: '二线城市' },
  { value: 4, label: '三线城市' },
  { value: 5, label: '四线城市' },
  { value: 6, 'label': '五线城市' }
];

const data = reactive({
  form: {},
  queryParams: {
    name: null,
	pageSize:10000000
  },
  rules: {
    name: [
      { required: true, message: "地区名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 监听 form.parentId 的变化，自动设置层级
watch(() => form.value.parentId, (newParentId) => {
  if (newParentId) {
    // 选择了父级，说明是城市
    form.value.level = 2;
  } else {
    // 未选择父级，说明是省份
    form.value.level = 1;
  }
});

/** 查询地区树结构列表 */
function getList() {
  loading.value = true;
  listRegion(queryParams.value).then(response => {
    // 【修复点】: 使用 response.rows 而不是 response.data
    regionList.value = proxy.handleTree(response.rows, "id", "parentId");
    loading.value = false;
  });
}

/** 查询用于下拉选择的地区树 */
function getTreeselect() {
  listRegion(queryParams.value).then(response => {
    // 【修复点】: 使用 response.rows 而不是 response.data
    const provinces = response.rows.filter(item => item.level === 1);
    regionOptions.value = proxy.handleTree(provinces, "id", "parentId");
  });
}

// 获取城市等级标签的文本
function getTagLabel(tagValue) {
  const tag = cityTagOptions.find(item => item.value == tagValue);
  return tag ? tag.label : '';
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    parentId: null,
    name: null,
    level: 1, // 默认为省份
    sortOrder: 0,
    tag: null,
    remark: null
  };
  proxy.resetForm("regionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.id) {
    // 如果传入了row，说明是“新增城市”
    form.value.parentId = row.id;
    title.value = `在“${row.name}”下添加城市`;
  } else {
    // 否则是“新增省份”
    title.value = "添加省份";
  }
  open.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getTreeselect();
  const _id = row.id;
  getRegion(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改地区信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["regionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateRegion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRegion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项？').then(function() {
    return delRegion(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

getList();
</script>