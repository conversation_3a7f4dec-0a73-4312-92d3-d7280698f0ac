<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="退款单号" prop="refundNo">
        <el-input v-model="queryParams.refundNo" placeholder="请输入退款单号" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="店铺名称" prop="storeName">
        <el-input v-model="queryParams.storeName" placeholder="请输入店铺名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="申请用户" prop="customerUserName">
        <el-input v-model="queryParams.customerUserName" placeholder="请输入申请用户名" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="退款状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择退款状态" clearable>
          <el-option v-for="dict in refundStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['buy:storeRefund:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['buy:storeRefund:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['buy:storeRefund:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="storeRefundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退款ID" align="center" prop="refundId" />
      <el-table-column label="退款单号" align="center" prop="refundNo" width="200" show-overflow-tooltip/>
      <el-table-column label="店铺名称" align="center" prop="store.storeName" show-overflow-tooltip/>
      <el-table-column label="用户手机号" align="center" prop="customerUser.userName" />
      <el-table-column label="退款金额" align="center" prop="refundAmount">
        <template #default="scope">
          <span>{{ '￥' + parseFloat(scope.row.refundAmount).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" align="center" prop="status">
        <template #default="scope">
          <span>{{ getStatusLabel(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
       <el-table-column label="处理时间" align="center" prop="handleTime" width="180">
         <template #default="scope">
           <span>{{ parseTime(scope.row.handleTime) }}</span>
         </template>
       </el-table-column>
      <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button v-if="scope.row.status == 0" link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:storeRefund:edit']">处理</el-button>
          <el-button v-else link type="primary" icon="View" @click="handleUpdate(scope.row)" v-hasPermi="['buy:storeRefund:query']">查看</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:storeRefund:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="storeRefundRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="退款单号" prop="refundNo">
              <el-input v-model="form.refundNo" placeholder="退款单号" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款金额" prop="refundAmount">
               <el-input-number v-model="form.refundAmount" :precision="2" style="width: 100%;" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
             <el-form-item label="关联店铺" prop="storeId">
                <el-input v-model="form.store.storeName" disabled></el-input>
             </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户手机号" prop="customerUserId">
              <el-input v-model="form.customerUser.userName" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="申请原因" prop="refundReason">
          <el-input v-model="form.refundReason" type="textarea" placeholder="申请退款原因" :disabled="true"/>
        </el-form-item>

        <div v-if="form.refundId != null">
           <el-divider />
           <div v-if="originalStatus === 0">
             <el-form-item label="处理操作" prop="status">
               <el-radio-group v-model="form.status">
                 <el-radio v-for="dict in processingStatusOptions" :key="dict.value" :value="parseInt(dict.value)">
                   {{dict.label}}
                 </el-radio>
               </el-radio-group>
             </el-form-item>
             <el-form-item label="处理备注" prop="remark" v-if="form.status == 2">
               <el-input v-model="form.remark" type="textarea" placeholder="请输入拒绝退款的原因或其他备注(可选)" />
             </el-form-item>
           </div>
           
           <div v-else>
             <el-form-item label="当前状态">
               <el-tag>{{ getStatusLabel(form.status) }}</el-tag>
             </el-form-item>
             <el-form-item label="处理备注" v-if="form.remark">
                <el-input v-model="form.remark" type="textarea" disabled />
             </el-form-item>
             <el-form-item label="交易号" prop="transactionId" v-if="form.status == 3">
               <el-input v-model="form.transactionId" disabled />
             </el-form-item>
           </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <template v-if="originalStatus === 0">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </template>
          <template v-else>
             <el-button @click="cancel">关 闭</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StoreRefund">
import { listStoreRefund, getStoreRefund, delStoreRefund, updateStoreRefund } from "@/api/buy/storeRefund";

const { proxy } = getCurrentInstance();

// 修改点: 全部的状态选项，用于搜索和表格展示
const refundStatusOptions = [
  { value: '0', label: '待处理' },
  { value: '1', label: '同意退款' },
  { value: '2', label: '拒绝退款' },
  { value: '3', label: '退款成功' },
  { value: '4', label: '退款关闭' }
];

// 修改点: 仅用于处理时显示的状态选项
const processingStatusOptions = [
  { value: '1', label: '同意退款' },
  { value: '2', label: '拒绝退款' }
];

const storeRefundList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 修改点: 增加一个变量来记录打开弹窗时的原始状态
const originalStatus = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    refundNo: null,
    storeName: null,
    customerUserName: null,
    status: null,
  },
  rules: {
    // 备注为非必填，所以无需添加校验规则
  }
});

const { queryParams, form, rules } = toRefs(data);

function getStatusLabel(statusValue) {
  const status = refundStatusOptions.find(item => item.value == statusValue);
  return status ? status.label : '未知';
}

/** 查询商家退款列表 */
function getList() {
  loading.value = true;
  listStoreRefund(queryParams.value).then(response => {
    // 假设后端返回的数据中包含了 store 和 customerUser 对象
    storeRefundList.value = response.rows.map(row => {
        row.storeName = row.store ? row.store.storeName : 'N/A';
        row.userName = row.customerUser ? row.customerUser.userName : 'N/A';
        return row;
    });
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    refundId: null,
    refundNo: null,
    storeId: null,
    customerUserId: null,
    refundAmount: null,
    refundReason: null,
    status: null, // 初始状态设为null
    transactionId: null,
    handleTime: null,
    remark: null,
    store: { storeName: '' }, // 初始化关联对象，防止报错
    customerUser: { userName: '' }
  };
  originalStatus.value = null;
  proxy.resetForm("storeRefundRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.refundId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 处理/查看按钮操作 */
async function handleUpdate(row) {
  reset();
  const _refundId = row.refundId || ids.value[0];
  const response = await getStoreRefund(_refundId);
  
  // 确保关联对象存在，避免模板渲染出错
  response.data.store = response.data.store || { storeName: '未知店铺' };
  response.data.customerUser = response.data.customerUser || { userName: '未知用户' };

  form.value = response.data;
  
  // 修改点: 记录打开时的原始状态
  originalStatus.value = form.value.status;
  
  // 修改点: 如果是待处理状态，默认选中“同意退款”
  if (form.value.status === 0) {
    form.value.status = 1;
  }
  
  open.value = true;
  title.value = originalStatus.value === 0 ? "处理退款申请" : "查看退款详情";
}


/** 提交按钮 */
function submitForm() {
  proxy.$refs["storeRefundRef"].validate(valid => {
    if (valid) {
      const submitData = {
        refundId: form.value.refundId,
        status: form.value.status,
        remark: form.value.remark
      };

      updateStoreRefund(submitData).then(response => {
        proxy.$modal.msgSuccess("操作成功");
        open.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _refundIds = row.refundId || ids.value;
  proxy.$modal.confirm('是否确认删除商家退款编号为"' + _refundIds + '"的数据项？').then(function() {
    return delStoreRefund(_refundIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('buy/storeRefund/export', {
    ...queryParams.value
  }, `storeRefund_${new Date().getTime()}.xlsx`)
}

getList();
</script>