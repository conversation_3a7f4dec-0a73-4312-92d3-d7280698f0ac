<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="节点名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入节点名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['buy:nodes:add']"
        >新增顶级节点</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Sort"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="mainTreeData"
      row-key="id"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="节点名称" prop="name" :show-overflow-tooltip="true" width="250"></el-table-column>
      <el-table-column label="节点类型" align="center" prop="nodeType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.nodeType === 'city'" type="success">城市</el-tag>
          <el-tag v-else-if="scope.row.nodeType === 'market'" type="primary">一级市场</el-tag>
          <el-tag v-else-if="scope.row.nodeType === 'market-two'" type="primary" effect="light">二级市场</el-tag>
          <el-tag v-else-if="scope.row.nodeType === 'category' && scope.row.parentType === 'market'" type="info">行业</el-tag>
          <el-tag v-else-if="scope.row.nodeType === 'category' && scope.row.parentType === 'category'" type="warning">分类</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="图标" align="center" prop="iconUrl">
        <template #default="scope">
          <image-preview :src="scope.row.iconUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="轮播图" align="center" prop="swiperPic">
        <template #default="scope">
          <image-preview :src="scope.row.swiperPic" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template #default="scope">
          <el-button 
            link 
            type="warning" 
            icon="OfficeBuilding" 
            @click="handleManageSecondaryMarkets(scope.row)" 
            v-if="scope.row.nodeType === 'market' && (!scope.row.parentType || scope.row.parentType === 'city')"
          >管理二级市场</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:nodes:edit']">修改</el-button>
          <el-button 
            link 
            type="primary" 
            icon="Plus" 
            @click="handleAddChild(scope.row)" 
            v-if="!(scope.row.nodeType === 'market-two' || (scope.row.nodeType === 'category' && scope.row.parentType === 'category'))"
            v-hasPermi="['buy:nodes:add']">新增下级</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:nodes:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="nodesRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="父节点" prop="parentId">
           <el-tree-select v-model="form.parentId" :data="nodesOptions" :props="{ value: 'id', label: 'name', children: 'children' }" value-key="id" placeholder="选择父节点" check-strictly disabled/>
        </el-form-item>
        <el-form-item label="节点类型" prop="nodeType">
          <el-select v-model="form.nodeType" placeholder="请选择节点类型" :disabled="isNodeTypeDisabled">
            <el-option v-for="item in nodeTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address" v-if="showAddressInput">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="节点图标" prop="iconUrl" v-if="form.nodeType === 'category'">
          <image-upload v-model="form.iconUrl" :limit="1" />
        </el-form-item>
        <el-form-item label="行业轮播图" prop="swiperPic" v-if="showIndustryFields">
           <image-upload v-model="form.swiperPic" :limit="9" />
           <div class="el-upload__tip">
              仅“行业”类型节点需要上传轮播图，最多9张。
           </div>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" placeholder="数字越小越靠前" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="secondaryMarketTitle" v-model="secondaryMarketOpen" width="800px" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAddSecondaryMarket">新增二级市场</el-button>
        </el-col>
      </el-row>
      <el-table :data="secondaryMarketList">
        <el-table-column label="二级市场名称" prop="name" />
        <el-table-column label="ID" prop="id" align="center"/>
        <el-table-column label="排序" prop="sortOrder" align="center"/>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Nodes">
import { handleTree } from "@/utils/ruoyi";
import ImageUpload from '@/components/ImageUpload';
import ImagePreview from '@/components/ImagePreview';
import { listNodes, getNodes, delNodes, addNodes, updateNodes } from "@/api/buy/nodes";

const { proxy } = getCurrentInstance();

const nodesList = ref([]);
const nodesOptions = ref([]); 
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(true); 
const refreshTable = ref(true); 
const isNodeTypeDisabled = ref(false);
const isParentDisabled = ref(false);
const nodeTypeOptions = ref([]);
const nodesMap = new Map();

const secondaryMarketOpen = ref(false);
const secondaryMarketTitle = ref("");
const currentPrimaryMarket = ref(null);
const secondaryMarketList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    name: null,
	  pageSize:100000000
  },
  rules: {
    name: [ { required: true, message: "节点名称不能为空", trigger: "blur" } ],
    nodeType: [ { required: true, message: "节点类型不能为空", trigger: "change" } ],
    sortOrder: [ { required: true, message: "排序权重不能为空", trigger: "blur" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 计算属性：只在一级市场显示地址输入框
const showAddressInput = computed(() => {
  if (form.value.nodeType !== 'market') return false;
  if (!form.value.parentId) return false;
  const parentNode = nodesMap.get(form.value.parentId);
  return parentNode && parentNode.nodeType === 'city';
});

// 【改动点】: 新增计算属性，用于控制行业轮播图的显示
const showIndustryFields = computed(() => {
  // 必须是 category 类型
  if (form.value.nodeType !== 'category') return false;
  // 父节点必须存在
  if (!form.value.parentId) return false;
  const parentNode = nodesMap.get(form.value.parentId);
  // 父节点必须是 market 类型
  return parentNode && parentNode.nodeType === 'market';
});


const mainTreeData = computed(() => {
  function filterNodes(nodes) {
    if (!nodes || nodes.length === 0) return [];
    return nodes.filter(node => {
      if (node.nodeType === 'market-two') {
        return false;
      }
      if (node.children && node.children.length > 0) {
        node.children = filterNodes(node.children);
      }
      return true;
    });
  }
  return filterNodes(JSON.parse(JSON.stringify(nodesList.value)));
});

watch(showAddressInput, (newValue) => {
  if (!newValue) {
    form.value.address = null;
  }
});

// 【改动点】: 新增watch，当轮播图隐藏时，清空数据
watch(showIndustryFields, (newValue) => {
    if (!newValue) {
        form.value.swiperPic = null;
    }
});

function getList() {
  loading.value = true;
  return listNodes(queryParams.value).then(response => {
    const flatList = response.rows || [];
    
    nodesMap.clear();
    flatList.forEach(node => nodesMap.set(node.id, node));
    flatList.forEach(node => {
      const parent = nodesMap.get(node.parentId);
      node.parentType = parent ? parent.nodeType : null;
    });

    const treeData = handleTree(flatList, "id", "parentId");
    nodesList.value = treeData;
    const options = [{ id: 0, name: '顶级节点', children: treeData }];
    nodesOptions.value = options;
    loading.value = false;
  });
}

function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    id: null,
    parentId: null,
    nodeType: null,
    name: null,
    address: null,
    iconUrl: null,
    swiperPic: null,
    sortOrder: 0,
  };
  isNodeTypeDisabled.value = false; 
  isParentDisabled.value = false;
  proxy.resetForm("nodesRef");
}

function handleQuery() {
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleAdd() {
  reset();
  form.value.parentId = 0;
  isParentDisabled.value = false;
  isNodeTypeDisabled.value = true;
  form.value.nodeType = 'city';
  nodeTypeOptions.value = [{ label: "城市", value: "city" }];
  open.value = true;
  title.value = "添加顶级节点 (城市)";
}

function handleAddChild(row) {
  reset();
  form.value.parentId = row.id;
  isParentDisabled.value = true;
  
  if (row.nodeType === 'city') {
    form.value.nodeType = 'market';
    isNodeTypeDisabled.value = true;
  } else if (row.nodeType === 'market' && row.parentType === 'city') {
    form.value.nodeType = null;
    isNodeTypeDisabled.value = false;
    nodeTypeOptions.value = [
      { label: "二级市场", value: "market-two" },
      { label: "行业", value: "category" }
    ];
  } else if (row.nodeType === 'category' && row.parentType === 'market') {
    form.value.nodeType = 'category';
    isNodeTypeDisabled.value = true;
  }
  
  open.value = true;
  title.value = "新增下级节点";
}

function handleUpdate(row) {
  reset();
  getNodes(row.id).then(response => {
    form.value = response.data;
    isNodeTypeDisabled.value = true;
    isParentDisabled.value = true;
    nodeTypeOptions.value = [
        { label: "城市", value: "city" },
        { label: "一级市场", value: "market" },
        { label: "二级市场", value: "market-two" },
        { label: "行业", value: "category" },
        { label: "分类", value: "category" }
    ];
    open.value = true;
    title.value = "修改节点";
  });
}

function submitForm() {
  proxy.$refs["nodesRef"].validate(async (valid) => {
    if (valid) {
      const isUpdate = form.value.id != null;
      const apiCall = isUpdate ? updateNodes(form.value) : addNodes(form.value);
      
      await apiCall;

      proxy.$modal.msgSuccess(isUpdate ? "修改成功" : "新增成功");
      open.value = false;
      
      await getList();
      
      if (secondaryMarketOpen.value) {
          const updatedPrimaryMarket = findNodeInTree(nodesList.value, currentPrimaryMarket.value.id);
          if (updatedPrimaryMarket) {
              handleManageSecondaryMarkets(updatedPrimaryMarket);
          } else {
              secondaryMarketOpen.value = false;
          }
      }
    }
  });
}

function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项？如果存在下级，将一并删除！').then(() => {
    return delNodes(row.id);
  }).then(async () => {
    await getList();
    
    if (secondaryMarketOpen.value) {
        if (row.id === currentPrimaryMarket.value.id) {
            secondaryMarketOpen.value = false;
        } else {
            const updatedPrimaryMarket = findNodeInTree(nodesList.value, currentPrimaryMarket.value.id);
            if (updatedPrimaryMarket) {
                handleManageSecondaryMarkets(updatedPrimaryMarket);
            }
        }
    }
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleManageSecondaryMarkets(row) {
    currentPrimaryMarket.value = row;
    secondaryMarketTitle.value = `管理二级市场 - ${row.name}`;
    const fullNode = findNodeInTree(nodesList.value, row.id);
    if (fullNode && fullNode.children) {
        secondaryMarketList.value = fullNode.children.filter(child => child.nodeType === 'market-two');
    } else {
        secondaryMarketList.value = [];
    }
    secondaryMarketOpen.value = true;
}

function handleAddSecondaryMarket() {
    reset();
    form.value.parentId = currentPrimaryMarket.value.id;
    form.value.nodeType = 'market-two';
    isParentDisabled.value = true;
    isNodeTypeDisabled.value = true;
    open.value = true;
    title.value = `为 [${currentPrimaryMarket.value.name}] 添加二级市场`;
}

function findNodeInTree(tree, nodeId) {
    for (const node of tree) {
        if (node.id === nodeId) return node;
        if (node.children) {
            const found = findNodeInTree(node.children, nodeId);
            if (found) return found;
        }
    }
    return null;
}

getList();
</script>