<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属店铺" prop="storeId">
        <el-select
           v-model="queryParams.storeId"
           placeholder="请输入店铺名称搜索"
           clearable
           filterable
           remote
           :remote-method="remoteSearchStoresForQuery"
           :loading="searchStoreLoading"
           style="width: 240px"
        >
          <el-option
            v-for="item in searchStoreList"
            :key="item.storeId"
            :label="item.storeName"
            :value="item.storeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['buy:product:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['buy:product:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['buy:product:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['buy:product:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品ID" align="center" prop="productId" />
      <el-table-column label="所属店铺" align="center" prop="storeName" />
      <el-table-column label="商品名称" align="center" prop="productName" />
      <el-table-column label="商品图片" align="center" prop="productImages" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.productImages" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:product:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:product:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="productRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属店铺" prop="storeId">
          <el-select
             v-model="form.storeId"
             placeholder="请输入店铺名称关键字搜索"
             filterable
             remote
             :remote-method="remoteStoreSearchForForm"
             :loading="formStoreLoading"
             style="width: 100%;"
          >
            <el-option
              v-for="item in formStoreList"
              :key="item.storeId"
              :label="item.storeName"
              :value="item.storeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入商品名称" />
        </el-form-item>
		    <el-form-item label="商品介绍" prop="dsc">
		      <el-input v-model="form.dsc" type="textarea" placeholder="请输入商品介绍" />
		    </el-form-item>
        <el-form-item label="商品图片" prop="productImages">
          <image-upload v-model="form.productImages"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Product">
import { listProduct, getProduct, delProduct, addProduct, updateProduct } from "@/api/buy/product";
import { listStoreInfo } from "@/api/buy/storeInfo";
const { proxy } = getCurrentInstance();

const productList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 【改动点】: 分离搜索栏和表单的店铺列表状态
const searchStoreList = ref([]);
const searchStoreLoading = ref(false);
const formStoreList = ref([]);
const formStoreLoading = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    storeId: null,
    categoryId: null,
    productName: null,
    productImages: null,
    isRecommended: null,
  },
  rules: {
    storeId: [ { required: true, message: "所属店铺不能为空", trigger: "change" } ],
    categoryId: [ { required: true, message: "所属分类ID不能为空", trigger: "blur" } ],
    productName: [ { required: true, message: "商品名称不能为空", trigger: "blur" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询商品信息列表 */
function getList() {
  loading.value = true;
  listProduct(queryParams.value).then(response => {
    productList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 【改动点】: 搜索栏的店铺远程搜索方法 */
function remoteSearchStoresForQuery(query) {
  if (query) {
    searchStoreLoading.value = true;
    listStoreInfo({ storeName: query, pageSize: 50 }).then(response => {
      searchStoreList.value = response.rows;
    }).finally(() => {
      searchStoreLoading.value = false;
    });
  } else {
    searchStoreList.value = [];
  }
}

/** 表单内的店铺远程搜索方法 */
function remoteStoreSearchForForm(query) {
  if (query) {
    formStoreLoading.value = true;
    listStoreInfo({ storeName: query, pageSize: 50 }).then(response => {
      formStoreList.value = response.rows;
    }).finally(() => {
      formStoreLoading.value = false;
    });
  } else {
    formStoreList.value = [];
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    productId: null,
    storeId: null,
    categoryId: null,
    productName: null,
    dsc: null,
    productImages: null,
    isRecommended: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  formStoreList.value = [];
  proxy.resetForm("productRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  searchStoreList.value = []; // 清空搜索栏的下拉选项
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.productId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加商品信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _productId = row.productId || ids.value[0];
  getProduct(_productId).then(response => {
    form.value = response.data;
    if (row.storeId && row.storeName) {
      formStoreList.value = [{ storeId: row.storeId, storeName: row.storeName }];
    }
    open.value = true;
    title.value = "修改商品信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["productRef"].validate(valid => {
    if (valid) {
      if (form.value.productId != null) {
        updateProduct(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addProduct(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _productIds = row.productId || ids.value;
  proxy.$modal.confirm('是否确认删除商品信息编号为"' + _productIds + '"的数据项？').then(function() {
    return delProduct(_productIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('buy:product/export', {
    ...queryParams.value
  }, `product_${new Date().getTime()}.xlsx`)
}

getList();
</script>