import request from '@/utils/request'

// 查询搜索分类列表
export function listCategory(query) {
  return request({
    url: '/buy/category/list',
    method: 'get',
    params: query
  })
}

// 查询搜索分类详细
export function getCategory(categoryId) {
  return request({
    url: '/buy/category/' + categoryId,
    method: 'get'
  })
}

// 新增搜索分类
export function addCategory(data) {
  return request({
    url: '/buy/category',
    method: 'post',
    data: data
  })
}

// 修改搜索分类
export function updateCategory(data) {
  return request({
    url: '/buy/category',
    method: 'put',
    data: data
  })
}

// 删除搜索分类
export function delCategory(categoryId) {
  return request({
    url: '/buy/category/' + categoryId,
    method: 'delete'
  })
}
