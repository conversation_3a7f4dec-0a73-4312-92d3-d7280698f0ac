import request from '@/utils/request'

// 查询商户向用户转账记录列表
export function getTransferAmount() {
  return request({
    url: '/buy/merchantTransfer/transferAmount'
  })
}

export function updateTransferAmount(data) {
  return request({
    url: '/buy/merchantTransfer/updateTransferAmount',
    method: 'put',
    data: data
  })
}

export function updateConfigImage(imageUrl) {
  return request({
    url: '/buy/merchantTransfer/configImage',
    method: 'put',
    data: { imageUrl: imageUrl }
  });
}

// 【改动点】: 获取配置图片路径
export function getConfigImage() {
  return request({
    url: '/buy/merchantTransfer/configImage',
    method: 'get'
  });
}

// 【新增】更新第二个配置图片路径
export function updateAnotherConfigImage(imageUrl) {
  return request({
    url: '/buy/merchantTransfer/anotherConfigImage',
    method: 'put',
    data: { imageUrl: imageUrl }
  });
}

// 【新增】获取第二个配置图片路径
export function getAnotherConfigImage() {
  return request({
    url: '/buy/merchantTransfer/anotherConfigImage',
    method: 'get'
  });
}