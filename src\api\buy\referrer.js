import request from '@/utils/request'

// 查询推荐人信息列表
export function listReferrer(query) {
  return request({
    url: '/buy/referrer/list',
    method: 'get',
    params: query
  })
}

// 查询推荐人信息详细
export function getReferrer(id) {
  return request({
    url: '/buy/referrer/' + id,
    method: 'get'
  })
}

// 新增推荐人信息
export function addReferrer(data) {
  return request({
    url: '/buy/referrer',
    method: 'post',
    data: data
  })
}

// 修改推荐人信息
export function updateReferrer(data) {
  return request({
    url: '/buy/referrer',
    method: 'put',
    data: data
  })
}

// 删除推荐人信息
export function delReferrer(id) {
  return request({
    url: '/buy/referrer/' + id,
    method: 'delete'
  })
}
