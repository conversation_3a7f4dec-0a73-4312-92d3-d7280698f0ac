<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="店铺名称" prop="storeName">
        <el-input v-model="queryParams.storeName" placeholder="请输入店铺名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="queryParams.contactPerson" placeholder="请输入联系人" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="市场筛选" prop="marketFilter">
        <el-cascader
          v-model="queryParams.marketFilter"
          :options="marketFilterOptions"
          :props="{ value: 'value', label: 'label', children: 'children' }"
          placeholder="请选择市场"
          clearable
          @change="handleMarketFilterChange"
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="dict in audit_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="余额排序" prop="moneySort">
        <el-select v-model="queryParams.moneySort" placeholder="请选择排序方式" clearable style="width: 120px;">
          <el-option label="升序" value="asc"/>
          <el-option label="降序" value="desc"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['buy:storeInfo:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['buy:storeInfo:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['buy:storeInfo:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['buy:storeInfo:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="storeInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商店ID" align="center" prop="storeId" />
      <el-table-column label="店主用户ID" align="center" prop="userId" />
      <el-table-column label="店铺名称" align="center" prop="storeName" show-overflow-tooltip />
      <el-table-column label="店铺头像" align="center" prop="avatar" width="100">
        <template #default="scope"><image-preview :src="scope.row.avatar" :width="50" :height="50"/></template>
      </el-table-column>
      <el-table-column label="品牌" align="center" prop="brand" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="phone" width="120"/>
      <el-table-column label="市场及街区" align="center" prop="marketAddress" width="180" show-overflow-tooltip/>
      <el-table-column label="入驻时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺余额" align="center" prop="money" />
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template #default="scope"><dict-tag :options="audit_status" :value="scope.row.auditStatus"/></template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button v-if="scope.row.auditStatus == '0'" link type="success" icon="Check" @click="handleAudit(scope.row)" v-hasPermi="['buy:storeInfo:edit']">审核</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:storeInfo:edit']">修改</el-button>
          <el-button link type="info" icon="Goods" @click="handleViewProducts(scope.row)">查看店铺商品</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:storeInfo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="storeInfoRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="关联的用户" prop="userId">
          <el-select
            v-model="form.userId"
            placeholder="请输入关键字搜索用户 (可不选)"
            filterable
            remote
            :remote-method="remoteUserSearch"
            :loading="userLoading"
            style="width: 100%;"
            clearable
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName + (user.nickName ? ` (${user.nickName})` : '')"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺名称" prop="storeName"><el-input v-model="form.storeName" placeholder="请输入店铺名称" /></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand"><el-input v-model="form.brand" placeholder="请输入品牌" /></el-form-item>
          </el-col>
        </el-row>
        <el-row>
           <el-col :span="12">
            <el-form-item label="店铺等级" prop="levelId">
              <el-select v-model="form.levelId" placeholder="请选择店铺等级" clearable style="width: 100%;">
                <el-option v-for="level in storeLevelOptions" :key="level.id" :label="level.levelName" :value="level.id"/>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="12">
             <el-form-item label="联系人" prop="contactPerson"><el-input v-model="form.contactPerson" placeholder="请输入联系人" /></el-form-item>
           </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入驻时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择入驻时间"
                style="width: 100%;"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone"><el-input v-model="form.phone" placeholder="请输入联系电话" /></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address"><el-input v-model="form.address" placeholder="请输入详细地址" /></el-form-item>
          </el-col>
        </el-row>
        <el-row>
           <el-col :span="12">
             <el-form-item label="店铺余额" prop="money"><el-input-number v-model="form.money" :precision="2" placeholder="请输入余额" style="width: 100%;"/></el-form-item>
           </el-col>
           <el-col :span="12">
            <el-form-item label="经度" prop="longitude"><el-input-number v-model="form.longitude" :precision="7" placeholder="请输入经度" style="width: 100%;"/></el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude"><el-input-number v-model="form.latitude" :precision="7" placeholder="请输入纬度" style="width: 100%;"/></el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="主营业务" prop="mainBusiness"><el-input v-model="form.mainBusiness" type="textarea" placeholder="请输入内容" /></el-form-item>
        <el-form-item label="会员说明" prop="membershipDesc"><el-input v-model="form.membershipDesc" type="textarea" placeholder="请输入内容" /></el-form-item>
        <el-row>
           <el-col :span="8">
               <el-form-item label="店铺头像" prop="avatar"><image-upload v-model="form.avatar"/></el-form-item>
           </el-col>
           <el-col :span="8">
               <el-form-item label="微信二维码" prop="wechatQrcode"><image-upload v-model="form.wechatQrcode"/></el-form-item>
           </el-col>
            <el-col :span="8">
               <el-form-item label="视频" prop="videoUrl"><file-upload v-model="form.videoUrl"/></el-form-item>
           </el-col>
        </el-row>
        <el-form-item label="店铺展示图" prop="displayImages"><image-upload v-model="form.displayImages" :limit="9"/></el-form-item>
        <el-form-item label="库房/工厂大图" prop="factoryImages"><image-upload v-model="form.factoryImages" :limit="9"/></el-form-item>
        <el-form-item label="库房/工厂展示图" prop="factoryDisplayImages"><image-upload v-model="form.factoryDisplayImages" :limit="9"/></el-form-item>
        <el-form-item label="店铺资质图片" prop="qualificationImages"><image-upload v-model="form.qualificationImages" :limit="9"/></el-form-item>
        <el-divider content-position="center">状态与统计</el-divider>
        <el-row>
           <el-col :span="12">
             <el-form-item label="访客量" prop="visitorCount"><el-input-number v-model="form.visitorCount" placeholder="请输入访客量" /></el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="人气值" prop="popularityScore"><el-input-number v-model="form.popularityScore" placeholder="请输入人气值" /></el-form-item>
           </el-col>
        </el-row>
        <el-row>
           <el-col :span="12">
             <el-form-item label="展示访客信息" prop="showVisitorInfo">
               <el-switch v-model="form.showVisitorInfo" active-value="1" inactive-value="0"></el-switch>
             </el-form-item>
           </el-col>
        </el-row>

        <div v-if="form.storeId">
          <el-divider content-position="center">审核与标签设置</el-divider>
           <el-form-item label="市场及街区" prop="marketAddress">
             <el-cascader
               v-model="selectedMarket"
               :options="marketCascaderOptions"
               :props="{ value: 'id', label: 'name', children: 'children', multiple: true }"
               :show-all-levels="false"
               @change="handleMarketChange"
               placeholder="可多选城市/市场"
               style="width: 100%;"
               clearable
               collapse-tags
               collapse-tags-tooltip
             />
           </el-form-item>
          <el-row>
            <el-col :span="24">
               <el-form-item label="审核状态" prop="auditStatus">
                 <el-radio-group v-model="form.auditStatus">
                   <el-radio v-for="dict in audit_status" :key="dict.value" :value="parseInt(dict.value)">{{dict.label}}</el-radio>
                 </el-radio-group>
               </el-form-item>
            </el-col>
          </el-row>
          <div v-if="form.auditStatus === 1">
               <el-form-item label="选择二级市场" prop="marketTwo" v-if="level3MarketOptions.length > 0">
                 <el-checkbox-group v-model="form.marketTwo">
                   <el-checkbox v-for="node in level3MarketOptions" :key="node.id" :label="node.name">{{ node.name }}</el-checkbox>
                 </el-checkbox-group>
               </el-form-item>

               <el-form-item label="选择业务" prop="level3NodeIds">
                 <el-checkbox-group v-model="form.level3NodeIds" style="width: 100%;">
                    <div v-if="groupedBusinessOptions.length > 0">
                      <div v-for="marketGroup in groupedBusinessOptions" :key="marketGroup.marketId" class="business-group">
                        <p class="group-title">{{ marketGroup.marketName }}</p>
                        <el-checkbox v-for="node in marketGroup.businesses" :key="node.id" :label="node.id">{{ node.name }}</el-checkbox>
                      </div>
                    </div>
                    <div v-else>
                      <el-text type="danger">未根据店铺的“市场及街区”信息找到对应的行业分类，请检查节点配置。</el-text>
                    </div>
                 </el-checkbox-group>
               </el-form-item>

               <el-form-item label="选择标签" prop="level4NodeIds" v-if="form.level3NodeIds && form.level3NodeIds.length > 0">
                 <el-checkbox-group v-model="form.level4NodeIds" style="width: 100%;">
                     <div v-for="item in selectedBusinessNodes" :key="item.businessId" class="business-group">
                         <p class="group-title">{{ item.marketName }} - {{ item.businessName }}</p>
                         <template v-if="item.tags && item.tags.length > 0">
                           <el-checkbox v-for="tagNode in item.tags" :key="tagNode.id" :label="tagNode.id" style="margin-right: 20px;">
                               {{ tagNode.name }}
                           </el-checkbox>
                         </template>
                         <el-text v-else type="info">该业务下暂无可用标签</el-text>
                     </div>
                 </el-checkbox-group>
               </el-form-item>

               <el-form-item label="选择标签" v-else>
                   <el-text type="info">请先选择业务。</el-text>
               </el-form-item>
          </div>
          <el-form-item label="审核意见" prop="auditRemark" v-if="form.auditStatus === 2">
            <el-input v-model="form.auditRemark" type="textarea" placeholder="请输入审核不通过的原因" />
          </el-form-item>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 商品管理弹窗 -->
    <el-dialog title="店铺商品管理" v-model="productDialogOpen" width="1200px" append-to-body>
      <div class="product-dialog-content">
        <!-- 商品搜索表单 -->
        <el-form :model="productQueryParams" ref="productQueryRef" :inline="true" label-width="80px" class="mb-4">
          <el-form-item label="商品名称" prop="productName">
            <el-input v-model="productQueryParams.productName" placeholder="请输入商品名称" clearable @keyup.enter="getProductList"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleProductQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetProductQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 商品操作按钮 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAddProduct">新增商品</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="productSingle" @click="handleUpdateProduct">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="productMultiple" @click="handleDeleteProduct">删除</el-button>
          </el-col>
        </el-row>

        <!-- 商品表格 -->
        <el-table v-loading="productLoading" :data="productList" @selection-change="handleProductSelectionChange" max-height="400">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="商品ID" align="center" prop="productId" width="80" />
          <el-table-column label="商品名称" align="center" prop="productName" show-overflow-tooltip />
          <el-table-column label="商品图片" align="center" prop="productImages" width="100">
            <template #default="scope">
              <image-preview :src="scope.row.productImages" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品介绍" align="center" prop="dsc" show-overflow-tooltip />
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleViewProduct(scope.row)">查看</el-button>
              <el-button link type="primary" icon="Edit" @click="handleUpdateProduct(scope.row)">修改</el-button>
              <el-button link type="danger" icon="Delete" @click="handleDeleteProduct(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 商品分页 -->
        <pagination
          v-show="productTotal > 0"
          :total="productTotal"
          v-model:page="productQueryParams.pageNum"
          v-model:limit="productQueryParams.pageSize"
          @pagination="getProductList"
          class="mt-4"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="productDialogOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 商品详情/编辑弹窗 -->
    <el-dialog :title="productTitle" v-model="productFormOpen" width="600px" append-to-body>
      <el-form ref="productFormRef" :model="productForm" :rules="productRules" label-width="100px">
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="productForm.productName" placeholder="请输入商品名称" :disabled="productViewMode" />
        </el-form-item>
        <el-form-item label="商品介绍" prop="dsc">
          <el-input v-model="productForm.dsc" type="textarea" placeholder="请输入商品介绍" :disabled="productViewMode" />
        </el-form-item>
        <el-form-item label="商品图片" prop="productImages">
          <image-upload v-model="productForm.productImages" :disabled="productViewMode"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitProductForm" v-if="!productViewMode">确 定</el-button>
          <el-button @click="cancelProduct">{{ productViewMode ? '关 闭' : '取 消' }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.business-group {
  width: 100%;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.business-group:last-child {
  border-bottom: none;
}
.group-title {
  font-weight: bold;
  margin: 5px 0;
  padding-bottom: 5px;
  color: #606266;
}

.product-dialog-content {
  padding: 0;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>

<script setup name="StoreInfo">
import { listStoreInfo, getStoreInfo, delStoreInfo, addStoreInfo, updateStoreInfo } from "@/api/buy/storeInfo";
import { listNodes } from "@/api/buy/nodes"; 
import { listStoreLevel } from "@/api/buy/storeLevel";
import { listUser, getUser } from "@/api/system/user";
import { nextTick, computed } from 'vue';
import { listProduct, getProduct, delProduct, addProduct, updateProduct } from "@/api/buy/product";

const { proxy } = getCurrentInstance();
const { audit_status } = proxy.useDict('audit_status');

const storeInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const allNodes = ref([]); 
const groupedBusinessOptions = ref([]);
const level3MarketOptions = ref([]);
const flatNodes = ref([]); 
const storeLevelOptions = ref([]);
const userList = ref([]);
const userLoading = ref(false);
const marketCascaderOptions = ref([]);
const selectedMarket = ref([]);
const originalUserId = ref(null);

// 市场筛选相关数据
const marketFilterOptions = ref([]);

// 商品相关数据
const productDialogOpen = ref(false);
const productFormOpen = ref(false);
const productLoading = ref(false);
const productList = ref([]);
const productTotal = ref(0);
const productSingle = ref(true);
const productMultiple = ref(true);
const productIds = ref([]);
const productTitle = ref("");
const productViewMode = ref(false);
const currentStoreId = ref(null);

const selectedBusinessNodes = computed(() => {
  if (!form.value.level3NodeIds || form.value.level3NodeIds.length === 0 || groupedBusinessOptions.value.length === 0) {
    return [];
  }
  const result = [];
  const selectedIds = new Set(form.value.level3NodeIds);

  groupedBusinessOptions.value.forEach(marketGroup => {
    marketGroup.businesses.forEach(business => {
      if (selectedIds.has(business.id)) {
        result.push({
          marketName: marketGroup.marketName,
          businessId: business.id,
          businessName: business.name,
          tags: business.children || []
        });
      }
    });
  });
  return result;
});


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    storeName: null,
    contactPerson: null,
    phone: null,
    auditStatus: null,
    marketFilter: null,
    marketAddress: null,
    marketTwo: null,
    moneySort: null,
  },
  rules: {
    storeName: [ { required: true, message: "店铺名称不能为空", trigger: "blur" } ],
  },
  // 商品相关数据
  productQueryParams: {
    pageNum: 1,
    pageSize: 10,
    productName: null,
    storeId: null,
  },
  productForm: {},
  productRules: {
    productName: [{ required: true, message: "商品名称不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules, productQueryParams, productForm, productRules } = toRefs(data);

function buildTree(nodes) {
  if (!nodes || nodes.length === 0) return [];
  flatNodes.value = nodes; 
  const map = {};
  const tree = [];
  
  nodes.forEach(node => {
    node.children = [];
    map[node.id] = node;
  });
  
  nodes.forEach(node => {
    const parent = map[node.parentId];
    if (parent) {
      parent.children.push(node);
    } else {
      tree.push(node);
    }
  });
  
  return tree;
}

watch(() => form.value.level3NodeIds, (newBusinessIds) => {
  if (!form.value.level4NodeIds || form.value.level4NodeIds.length === 0) return;
  
  const allValidTagIds = groupedBusinessOptions.value
    .flatMap(market => market.businesses) 
    .filter(businessNode => newBusinessIds.includes(businessNode.id)) 
    .flatMap(businessNode => (businessNode.children ? businessNode.children.map(tagNode => tagNode.id) : [])); 

  form.value.level4NodeIds = form.value.level4NodeIds.filter(tagId => allValidTagIds.includes(tagId));
}, { deep: true });


function getList() {
  loading.value = true;
  listStoreInfo(queryParams.value).then(response => {
    storeInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function getStoreLevels() {
  listStoreLevel().then(response => {
    storeLevelOptions.value = response.rows;
  });
}

function getMarketFilterOptions() {
  listNodes({ pageSize: 10000 }).then(response => {
    const allNodes = response.rows || [];
    const cityNodes = allNodes.filter(n => n.nodeType === 'city');
    const marketNodes = allNodes.filter(n => n.nodeType === 'market');
    const marketTwoNodes = allNodes.filter(n => n.nodeType === 'market-two');

    const options = cityNodes.map(city => {
      const childrenMarkets = marketNodes
        .filter(market => market.parentId === city.id)
        .map(market => {
          // 查找二级市场
          const childrenMarketTwo = marketTwoNodes
            .filter(marketTwo => marketTwo.parentId === market.id)
            .map(marketTwo => ({
              value: marketTwo.id,
              label: marketTwo.name,
              type: 'market-two'
            }));

          return {
            value: market.name,
            label: market.name,
            type: 'market',
            children: childrenMarketTwo.length > 0 ? childrenMarketTwo : undefined
          };
        });

      return {
        value: city.name,
        label: city.name,
        type: 'city',
        children: childrenMarkets.length > 0 ? childrenMarkets : undefined
      };
    });

    marketFilterOptions.value = options;
  });
}

function remoteUserSearch(query) {
  if (query) {
    userLoading.value = true;
    listUser({ userName: query, pageSize: 50 }).then(response => {
      userList.value = response.rows;
    }).finally(() => {
      userLoading.value = false;
    });
  } else {
    userList.value = [];
  }
}

function getMarketCascaderOptions() {
  return listNodes({ pageSize: 10000 }).then(response => {
    const all = response.rows || [];
    const cities = all.filter(n => n.nodeType === 'city');
    const markets = all.filter(n => n.nodeType === 'market');
    
    const options = cities.map(city => {
      const childrenMarkets = markets
        .filter(market => market.parentId === city.id)
        .map(m => ({ id: m.id, name: m.name }));
      
      return {
        id: city.id,
        name: city.name,
        children: childrenMarkets.length > 0 ? childrenMarkets : undefined
      };
    });
    marketCascaderOptions.value = options;
  });
}

function handleMarketFilterChange(value) {
  // 重置市场相关的查询参数
  queryParams.value.marketAddress = null;
  queryParams.value.marketTwo = null;

  if (!value || value.length === 0) {
    return;
  }

  // 根据选择的层级设置相应的查询参数
  if (value.length === 2) {
    // 选择了一级市场
    queryParams.value.marketAddress = value[1];
  } else if (value.length === 3) {
    // 选择了二级市场
    queryParams.value.marketTwo = value[2];
  }
}

function handleMarketChange(values) {
  if (!values || values.length === 0) {
    form.value.marketAddress = null;
    filterNodesByAddress(null);
    return;
  }

  const addressStrings = values.map(value => {
    const cityId = value[0];
    const marketId = value[1];
    const cityNode = marketCascaderOptions.value.find(c => c.id === cityId);
    const marketNode = cityNode ? cityNode.children.find(m => m.id === marketId) : null;
    if (cityNode && marketNode) {
      return `${cityNode.name} - ${marketNode.name}`;
    }
    return null;
  }).filter(Boolean);

  const finalAddressString = addressStrings.join(',');
  form.value.marketAddress = finalAddressString;
  filterNodesByAddress(finalAddressString);
}


async function openDialogWithData(storeId) {
  reset();
  getStoreLevels();
  proxy.$modal.loading("正在加载数据...");
  
  try {
    const [storeResponse, nodesResponse] = await Promise.all([
      getStoreInfo(storeId),
      listNodes({pageSize:100000000}),
      getMarketCascaderOptions()
    ]);

    if (nodesResponse && nodesResponse.rows) {
      allNodes.value = buildTree(nodesResponse.rows);
    } else {
      proxy.$modal.msgError("获取节点数据格式不正确。");
      return;
    }

    form.value = storeResponse.data;
    form.value.marketTwo = form.value.marketTwo ? form.value.marketTwo.split(',') : [];
    
    originalUserId.value = form.value.userId;
    title.value = "修改/审核商店信息";
    
    if (form.value.userId) {
      const userRes = await getUser(form.value.userId);
      userList.value = [userRes.data];
    }
    
    const address = form.value.marketAddress;
    if (address && marketCascaderOptions.value.length > 0) {
      const addressParts = address.split(',');
      const resolvedMarkets = [];
      addressParts.forEach(part => {
        const nameParts = part.split(' - ');
        if (nameParts.length === 2) {
          const cityName = nameParts[0].trim();
          const marketName = nameParts[1].trim();
          const cityNode = marketCascaderOptions.value.find(c => c.name === cityName);
          if (cityNode && cityNode.children) {
            const marketNode = cityNode.children.find(m => m.name === marketName);
            if (marketNode) {
              resolvedMarkets.push([cityNode.id, marketNode.id]);
            }
          }
        }
      });
      selectedMarket.value = resolvedMarkets;
    }
    
    filterNodesByAddress(form.value.marketAddress);
    await preselectTags(form.value.categoryTags);

    open.value = true;
  } catch (error) {
    console.error("加载数据失败:", error);
    proxy.$modal.msgError("加载数据失败，请重试。");
  } finally {
    proxy.$modal.closeLoading();
  }
}

function filterNodesByAddress(marketAddressesString) {
  groupedBusinessOptions.value = [];
  level3MarketOptions.value = [];
  if (!marketAddressesString || !allNodes.value || allNodes.value.length === 0) return;

  const addresses = marketAddressesString.split(',');
  const newGroupedBusinessOptions = [];
  const combinedLevel3MarketOptions = [];
  const addedMarketTwoIds = new Set();

  addresses.forEach(address => {
    const parts = address.split(' - ').map(s => s.trim());
    if (parts.length < 2) return;
    const [level1Name, level2Name] = parts;

    const level1Node = allNodes.value.find(node => node.name === level1Name);
    if (!level1Node || !level1Node.children) return;

    const level2Node = level1Node.children.find(node => node.name === level2Name);
    if (level2Node && level2Node.children) {
      const categoryNodes = level2Node.children.filter(child => child.nodeType === 'category');
      
      if (categoryNodes.length > 0) {
        newGroupedBusinessOptions.push({
          marketId: level2Node.id,
          marketName: level2Node.name,
          businesses: categoryNodes
        });
      }

      const marketTwoNodes = level2Node.children.filter(child => child.nodeType === 'market-two');
      marketTwoNodes.forEach(node => {
        if (!addedMarketTwoIds.has(node.id)) {
          combinedLevel3MarketOptions.push(node);
          addedMarketTwoIds.add(node.id);
        }
      });
    }
  });

  groupedBusinessOptions.value = newGroupedBusinessOptions;
  level3MarketOptions.value = combinedLevel3MarketOptions;
}

async function preselectTags(tags) {
  if (tags && typeof tags === 'string' && tags.length > 0) {
    const selectedTagIds = tags.split(',').map(id => parseInt(id, 10)); 
    if (selectedTagIds.length > 0) {
      const selectedBusinessIds = new Set();
      selectedTagIds.forEach(tagId => {
        const tagNode = flatNodes.value.find(n => n.id == tagId);
        if (tagNode && tagNode.parentId) {
            const businessExists = groupedBusinessOptions.value.some(market => 
                market.businesses.some(biz => biz.id == tagNode.parentId)
            );
            if (businessExists) {
                selectedBusinessIds.add(tagNode.parentId);
            }
        }
      });
      
      form.value.level3NodeIds = Array.from(selectedBusinessIds);
      await nextTick();
      form.value.level4NodeIds = selectedTagIds;
    }
  }
}


function handleAudit(row) { openDialogWithData(row.storeId); }
function handleUpdate(row) {
  const storeId = row.storeId || ids.value[0];
  openDialogWithData(storeId);
}

function submitForm() {
  proxy.$refs["storeInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.storeId != null && form.value.userId !== originalUserId.value) {
          proxy.$modal.confirm('您正在更改店铺的归属用户，这是一个高风险操作，请确认是否继续？', "警告", {
            confirmButtonText: "继续",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            proceedWithSubmit();
          }).catch(() => {});
        } else {
          proceedWithSubmit();
        }
    }
  });
}

function proceedWithSubmit() {
  const payload = { ...form.value };
  
  if (Array.isArray(payload.level3NodeIds) && payload.level3NodeIds.length > 0) {
    payload.industryId = payload.level3NodeIds.join(',');
  } else {
    payload.industryId = null;
  }

  if (payload.auditStatus === 1 && Array.isArray(payload.level4NodeIds) && payload.level4NodeIds.length > 0) {
    payload.categoryTags = payload.level4NodeIds.join(',');
  } else {
    payload.categoryTags = null;
  }
  
  if (Array.isArray(payload.marketTwo)) {
    payload.marketTwo = payload.marketTwo.join(',');
  }
  
  delete payload.level4NodeIds;
  delete payload.level3NodeIds;

  if (payload.storeId != null) {
    updateStoreInfo(payload).then(response => {
      proxy.$modal.msgSuccess("修改成功");
      open.value = false;
      getList();
    });
  } else {
    addStoreInfo(payload).then(response => {
      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      getList();
    });
  }
}

async function handleAdd() {
  reset();
  getStoreLevels();
  userList.value = [];
  open.value = true;
  title.value = "添加商店信息";
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    storeId: null, userId: null, storeName: null, avatar: null, brand: null,
    contactPerson: null, phone: null, mainBusiness: null, marketAddress: null, address: null,
    longitude: null, latitude: null, displayImages: null, factoryImages: null,
    factoryDisplayImages: null, videoUrl: null, wechatQrcode: null,
    qualificationImages: null, visitorCount: 0, popularityScore: 0, showVisitorInfo: "1",
    membershipDesc: null, levelId: null, money: 0.00, auditStatus: 0,
    auditBy: null, auditTime: null, auditRemark: null, categoryTags: null, 
    industryId: null, level3NodeIds: [], level4NodeIds: [],
    marketTwo: [], createBy: null, createTime: null, updateBy: null,
    updateTime: null, remark: null
  };
  allNodes.value = [];
  groupedBusinessOptions.value = [];
  level3MarketOptions.value = [];
  flatNodes.value = [];
  marketCascaderOptions.value = [];
  selectedMarket.value = [];
  originalUserId.value = null;
  proxy.resetForm("storeInfoRef");
}

function handleQuery() { queryParams.value.pageNum = 1; getList(); }
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.marketFilter = null;
  queryParams.value.marketAddress = null;
  queryParams.value.marketTwo = null;
  queryParams.value.moneySort = null;
  handleQuery();
}
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.storeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
function handleDelete(row) {
  const _storeIds = row.storeId || ids.value;
  proxy.$modal.confirm('是否确认删除商店信息编号为"' + _storeIds + '"的数据项？').then(function() {
    return delStoreInfo(_storeIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}
function handleExport() {
  proxy.download('buy:storeInfo/export', {
    ...queryParams.value
  }, `storeInfo_${new Date().getTime()}.xlsx`)
}

// 商品相关方法
function handleViewProducts(row) {
  currentStoreId.value = row.storeId;
  productQueryParams.value.storeId = row.storeId;
  productDialogOpen.value = true;
  getProductList();
}

function getProductList() {
  productLoading.value = true;
  listProduct(productQueryParams.value).then(response => {
    productList.value = response.rows;
    productTotal.value = response.total;
    productLoading.value = false;
  });
}

function handleProductQuery() {
  productQueryParams.value.pageNum = 1;
  getProductList();
}

function resetProductQuery() {
  proxy.resetForm("productQueryRef");
  handleProductQuery();
}

function handleProductSelectionChange(selection) {
  productIds.value = selection.map(item => item.productId);
  productSingle.value = selection.length != 1;
  productMultiple.value = !selection.length;
}

function handleAddProduct() {
  resetProductForm();
  productForm.value.storeId = currentStoreId.value;
  productTitle.value = "添加商品";
  productViewMode.value = false;
  productFormOpen.value = true;
}

function handleUpdateProduct(row) {
  resetProductForm();
  const productId = row.productId || productIds.value[0];
  getProduct(productId).then(response => {
    productForm.value = response.data;
    productTitle.value = "修改商品";
    productViewMode.value = false;
    productFormOpen.value = true;
  });
}

function handleViewProduct(row) {
  resetProductForm();
  getProduct(row.productId).then(response => {
    productForm.value = response.data;
    productTitle.value = "查看商品";
    productViewMode.value = true;
    productFormOpen.value = true;
  });
}

function handleDeleteProduct(row) {
  const _productIds = row.productId || productIds.value;
  proxy.$modal.confirm('是否确认删除商品编号为"' + _productIds + '"的数据项？').then(function() {
    return delProduct(_productIds);
  }).then(() => {
    getProductList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function submitProductForm() {
  proxy.$refs["productFormRef"].validate(valid => {
    if (valid) {
      if (productForm.value.productId != null) {
        updateProduct(productForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          productFormOpen.value = false;
          getProductList();
        });
      } else {
        addProduct(productForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          productFormOpen.value = false;
          getProductList();
        });
      }
    }
  });
}

function cancelProduct() {
  productFormOpen.value = false;
  resetProductForm();
}

function resetProductForm() {
  productForm.value = {
    productId: null,
    storeId: null,
    categoryId: null,
    productName: null,
    dsc: null,
    productImages: null,
    isRecommended: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("productFormRef");
}

getList();
getStoreLevels();
getMarketFilterOptions();
</script>