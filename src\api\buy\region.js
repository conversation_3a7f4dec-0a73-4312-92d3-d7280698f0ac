import request from '@/utils/request'

// 查询地区树结构列表
export function listRegion(query) {
  return request({
    url: '/buy/region/list',
    method: 'get',
    params: query
  })
}

// 查询地区树结构详细
export function getRegion(id) {
  return request({
    url: '/buy/region/' + id,
    method: 'get'
  })
}

// 新增地区树结构
export function addRegion(data) {
  return request({
    url: '/buy/region',
    method: 'post',
    data: data
  })
}

// 修改地区树结构
export function updateRegion(data) {
  return request({
    url: '/buy/region',
    method: 'put',
    data: data
  })
}

// 删除地区树结构
export function delRegion(id) {
  return request({
    url: '/buy/region/' + id,
    method: 'delete'
  })
}
