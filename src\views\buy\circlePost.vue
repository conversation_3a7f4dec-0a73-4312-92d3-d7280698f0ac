<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属圈子ID" prop="circleId">
        <el-input
          v-model="queryParams.circleId"
          placeholder="请输入所属圈子ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发帖用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入发帖用户ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['buy:circlePost:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['buy:circlePost:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['buy:circlePost:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['buy:circlePost:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="circlePostList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帖子ID" align="center" prop="postId" />
      <el-table-column label="所属圈子ID" align="center" prop="circleId" />
      <el-table-column label="发帖用户ID" align="center" prop="userId" />
      <el-table-column label="帖子内容" align="center" prop="content" show-overflow-tooltip />
      <el-table-column label="图片" align="center" prop="images" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="视频" align="center" prop="videoUrl" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="info" icon="View" @click="handleViewDetails(scope.row)" v-hasPermi="['buy:circleComment:list']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:circlePost:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:circlePost:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="circlePostRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属圈子ID" prop="circleId">
          <el-input v-model="form.circleId" placeholder="请输入所属圈子ID" />
        </el-form-item>
        <el-form-item label="发帖用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入发帖用户ID" />
        </el-form-item>
        <el-form-item label="帖子内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图片" prop="images">
          <image-upload v-model="form.images"/>
        </el-form-item>
        <el-form-item label="视频" prop="videoUrl">
          <file-upload v-model="form.videoUrl"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-drawer v-model="drawerOpen" :title="drawerTitle" direction="rtl" size="60%">
      <div class="drawer-container">
        <el-descriptions title="帖子信息" :column="2" border>
          <el-descriptions-item label="帖子ID">{{ currentPost.postId }}</el-descriptions-item>
          <el-descriptions-item label="发帖用户ID">{{ currentPost.userId }}</el-descriptions-item>
          <el-descriptions-item label="帖子内容" :span="2">{{ currentPost.content }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">评论列表</el-divider>

        <el-table v-loading="commentLoading" :data="commentList">
          <el-table-column label="评论ID" prop="commentId" align="center" />
          <el-table-column label="评论用户ID" prop="userId" align="center" />
          <el-table-column label="父评论ID" prop="parentId" align="center" />
          <el-table-column label="评论内容" prop="content" align="center" show-overflow-tooltip />
          <el-table-column label="评论时间" prop="createTime" align="center" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="commentTotal > 0"
          :total="commentTotal"
          v-model:page="commentQueryParams.pageNum"
          v-model:limit="commentQueryParams.pageSize"
          @pagination="getCommentList"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="CirclePost">
import { listCirclePost, getCirclePost, delCirclePost, addCirclePost, updateCirclePost } from "@/api/buy/circlePost";
import { listCircleComment } from "@/api/buy/circleComment";

const { proxy } = getCurrentInstance();

const circlePostList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);

// 详情抽屉相关状态变量
const drawerOpen = ref(false);
const drawerTitle = ref("");
const currentPost = ref({});
const commentLoading = ref(true);
const commentList = ref([]);
const commentTotal = ref(0);
const commentQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  postId: null,
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    circleId: null,
    userId: null,
    createTime: null,
  },
  rules: {
    circleId: [
      { required: true, message: "所属圈子ID不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "发帖用户ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询易购圈帖子列表 */
function getList() {
  loading.value = true;
  proxy.addDateRange(queryParams.value, daterangeCreateTime.value, 'CreateTime');
  listCirclePost(queryParams.value).then(response => {
    circlePostList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    postId: null,
    circleId: null,
    userId: null,
    content: null,
    images: null,
    videoUrl: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("circlePostRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreateTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.postId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加易购圈帖子";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _postId = row.postId || ids.value[0];
  getCirclePost(_postId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改易购圈帖子";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["circlePostRef"].validate(valid => {
    if (valid) {
      if (form.value.postId != null) {
        updateCirclePost(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCirclePost(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _postIds = row.postId || ids.value;
  proxy.$modal.confirm('是否确认删除易购圈帖子编号为"' + _postIds + '"的数据项？').then(function() {
    return delCirclePost(_postIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('buy/circlePost/export', {
    ...queryParams.value
  }, `circlePost_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleViewDetails(row) {
  currentPost.value = row;
  drawerTitle.value = `帖子详情 - ID: ${row.postId}`;
  
  commentQueryParams.pageNum = 1;
  commentQueryParams.postId = row.postId;

  getCommentList();
  
  drawerOpen.value = true;
}

/** 获取评论列表 */
function getCommentList() {
  commentLoading.value = true;
  listCircleComment(commentQueryParams).then(response => {
    commentList.value = response.rows;
    commentTotal.value = response.total;
    commentLoading.value = false;
  });
}

getList();
</script>

<style scoped>
.drawer-container {
  padding: 0 20px;
}
</style>