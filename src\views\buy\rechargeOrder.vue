<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="店铺名称" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入店铺名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable style="width: 200px">
          <el-option
            v-for="dict in rechargeOrderStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付时间">
        <el-date-picker
          v-model="daterangePaymentTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="rechargeOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="订单号" align="center" prop="orderNo" show-overflow-tooltip />
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="createBy" />
      <el-table-column label="店铺名称" align="center" prop="storeName" />
      <el-table-column label="充值金额" align="center" prop="rechargeAmount" />
      <el-table-column label="订单状态" align="center" prop="orderStatus">
         <template #default="scope">
           <span>{{ getStatusLabel(scope.row.orderStatus) }}</span>
         </template>
      </el-table-column>
      <el-table-column label="微信支付单号" align="center" prop="transactionId" show-overflow-tooltip />
      </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="rechargeOrderRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="订单号" :disabled="true" />
        </el-form-item>
        <el-form-item label="归属用户" prop="userId">
          <el-select v-model="form.userId" filterable placeholder="请选择用户" disabled>
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归属店铺" prop="storeId">
            <el-select v-model="form.storeId" filterable placeholder="请选择店铺" disabled>
             <el-option
               v-for="item in storeList"
               :key="item.storeId"
               :label="item.storeName"
               :value="item.storeId"
             ></el-option>
           </el-select>
        </el-form-item>
        <el-form-item label="充值金额" prop="rechargeAmount">
          <el-input-number v-model="form.rechargeAmount" :precision="2" :step="1" placeholder="请输入充值金额" />
        </el-form-item>

        <el-form-item label="订单状态" prop="orderStatus">
           <el-tag v-if="isFinalStatus">
             {{ getStatusLabel(form.orderStatus) }}
           </el-tag>
           <el-radio-group v-else v-model="form.orderStatus">
             <el-radio
               v-for="dict in availableStatusOptions"
               :key="dict.value"
               :label="parseInt(dict.value)"
             >{{dict.label}}</el-radio>
           </el-radio-group>
        </el-form-item>
        
        <el-form-item label="微信支付单号" prop="transactionId">
          <el-input v-model="form.transactionId" placeholder="微信支付成功后自动回填" disabled />
        </el-form-item>
        <el-form-item label="支付成功时间" prop="paymentTime">
          <el-date-picker clearable
            v-model="form.paymentTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择支付成功时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="订单完成时间" prop="completionTime">
          <el-date-picker clearable
            v-model="form.completionTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择订单完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isFinalStatus" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RechargeOrder">
import { listRechargeOrder, getRechargeOrder, delRechargeOrder, addRechargeOrder, updateRechargeOrder } from "@/api/buy/rechargeOrder";
import { listUser } from "@/api/system/user";
import { listStoreInfo } from "@/api/buy/storeInfo";

const { proxy } = getCurrentInstance();

// 【修改点】: 移除 useDict, 改为本地常量
const rechargeOrderStatusOptions = [
  { value: '0', label: '待支付' },
  { value: '1', label: '支付成功' },
  { value: '2', label: '已完成' },
  { value: '3', label: '已关闭' },
  { value: '4', label: '支付失败' }
];

const rechargeOrderList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 【保留】: 按照您的要求，此部分保持原样
const userList = ref([]);
const storeList = ref([]);

const daterangePaymentTime = ref([]);
const originalStatus = ref(null); // 记录打开弹窗时的原始状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    nickName: null,
    storeName: null,
    orderStatus: null,
  },
  rules: {
    userId: [ { required: true, message: "归属用户不能为空", trigger: "change" } ],
    storeId: [ { required: true, message: "归属店铺不能为空", trigger: "change" } ],
    rechargeAmount: [ { required: true, message: "充值金额不能为空", trigger: "blur" } ],
    orderStatus: [ { required: true, message: "订单状态不能为空", trigger: "change" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 【核心修改】: 计算属性，根据原始状态动态提供可操作的状态选项
const isFinalStatus = computed(() => [2, 3].includes(originalStatus.value));

const availableStatusOptions = computed(() => {
  const status = originalStatus.value;
  if (status === 0) { // 待支付 -> 可改为 支付成功 / 已关闭
    return rechargeOrderStatusOptions.filter(s => ['1', '3'].includes(s.value));
  }
  if (status === 1) { // 支付成功 -> 可改为 已完成 / 已关闭
    return rechargeOrderStatusOptions.filter(s => ['2', '3'].includes(s.value));
  }
  if (status === 4) { // 支付失败 -> 可改为 已关闭
     return rechargeOrderStatusOptions.filter(s => ['3'].includes(s.value));
  }
  return []; // 其他状态（已完成、已关闭）不允许操作
});

// 【修改点】: 新增本地函数用于在表格中显示状态文本
function getStatusLabel(statusValue) {
  const status = rechargeOrderStatusOptions.find(item => item.value == statusValue);
  return status ? status.label : '未知';
}

/** 查询店铺充值订单列表 */
function getList() {
  loading.value = true;
  proxy.addDateRange(queryParams.value, daterangePaymentTime.value, 'PaymentTime');
  listRechargeOrder(queryParams.value).then(response => {
    rechargeOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 【保留】: 按照您的要求，此部分保持原样
function getUserList() {
  listUser({ pageNum: 1, pageSize: 9999 }).then(response => {
    userList.value = response.rows;
  });
}
function getStoreList() {
  listStoreInfo({ pageNum: 1, pageSize: 9999 }).then(response => {
    storeList.value = response.rows;
  });
}


// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    orderId: null,
    orderNo: null,
    userId: null,
    storeId: null,
    rechargeAmount: null,
    orderStatus: 0,
    prepayId: null,
    transactionId: null,
    paymentTime: null,
    completionTime: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  originalStatus.value = null; // 重置原始状态
  proxy.resetForm("rechargeOrderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangePaymentTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.orderId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}


/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  // 【保留】: 按照您的要求，此部分保持原样
  getUserList();
  getStoreList();
  const _orderId = row.orderId || ids.value[0]
  getRechargeOrder(_orderId).then(response => {
    form.value = response.data;
    form.value.orderStatus = parseInt(form.value.orderStatus);
    originalStatus.value = form.value.orderStatus; // 记录原始状态
    open.value = true;
    title.value = isFinalStatus.value ? "查看订单详情" : "修改订单状态";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["rechargeOrderRef"].validate(valid => {
    if (valid) {
      if (form.value.orderId != null) {
        // 根据新状态设置完成时间
        if (form.value.orderStatus === 2 || form.value.orderStatus === 3) {
            form.value.completionTime = proxy.parseTime(new Date());
        }
        updateRechargeOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _orderIds = row.orderId || ids.value;
  proxy.$modal.confirm('是否确认删除店铺充值订单编号为"' + _orderIds + '"的数据项？').then(function() {
    return delRechargeOrder(_orderIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('buy/rechargeOrder/export', {
    ...queryParams.value
  }, `rechargeOrder_${new Date().getTime()}.xlsx`)
}

getList();
</script>