<template>
  <div class="app-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>红包转账金额设置</span>
        </div>
      </template>
      <div class="setting-body">
        <div class="current-setting">
          <span class="label">当前转账金额：</span>
          <span v-if="loadingAmount" class="value">加载中...</span>
          <span v-else class="value important">¥ {{ currentAmount || '未设置' }}</span>
        </div>
        <el-divider />
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" style="max-width: 500px;">
          <el-form-item label="设置新金额" prop="newAmount">
            <el-input-number
              v-model="form.newAmount"
              :precision="2"
              :step="0.1"
              :min="0.01"
              controls-position="right"
              placeholder="请输入大于0的金额"
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSubmitAmount">保存金额设置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>找货群二维码设置</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>当前图片</h3>
          <div class="current-image-wrapper">
             <image-preview v-if="currentImageUrl" :src="currentImageUrl" style="width: 100%; height: 200px;"/>
             <el-empty v-else description="暂未上传图片" />
          </div>
        </el-col>
        <el-col :span="12">
          <h3>上传/修改图片</h3>
          <image-upload v-model="newImageUrl" :limit="1" />
          <el-button type="primary" @click="handleSubmitImage" style="margin-top: 20px;">保存图片设置</el-button>
          <el-text v-if="newImageUrl" type="info" style="margin-left: 10px;">清空上传框并保存，即可删除图片</el-text>
        </el-col>
      </el-row>
    </el-card>

    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>找货页面上方文字设置</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>当前图片</h3>
          <div class="current-image-wrapper">
             <image-preview v-if="anotherCurrentImageUrl" :src="anotherCurrentImageUrl" style="width: 100%; height: 200px;"/>
             <el-empty v-else description="暂未上传图片" />
          </div>
        </el-col>
        <el-col :span="12">
          <h3>上传/修改图片</h3>
          <image-upload v-model="anotherNewImageUrl" :limit="1" />
          <el-button type="primary" @click="handleSubmitAnotherImage" style="margin-top: 20px;">保存图片设置</el-button>
           <el-text v-if="anotherNewImageUrl" type="info" style="margin-left: 10px;">清空上传框并保存，即可删除图片</el-text>
        </el-col>
      </el-row>
    </el-card>

  </div>
</template>

<script setup name="TransferAmountConfig">
import { getTransferAmount, updateTransferAmount, updateConfigImage, getConfigImage, updateAnotherConfigImage, getAnotherConfigImage } from "@/api/buy/merchantTransfer.js";

const { proxy } = getCurrentInstance();

const formRef = ref(null);
const currentAmount = ref(null);
const loadingAmount = ref(true);
const form = reactive({ newAmount: undefined });

const currentImageUrl = ref('');
const newImageUrl = ref(''); 
const anotherCurrentImageUrl = ref('');
const anotherNewImageUrl = ref('');

const rules = reactive({
  newAmount: [{ required: true, message: "新金额不能为空", trigger: "blur" }]
});

/** 获取当前转账金额配置 */
function getCurrentAmount() {
  loadingAmount.value = true;
  getTransferAmount().then(response => {
    currentAmount.value = response.data.amount;
  }).finally(() => {
    loadingAmount.value = false;
  });
}

/** 获取Redis中的图片路径 #1 */
function getRedisImage() {
  getConfigImage().then(response => {
    currentImageUrl.value = response.msg;
    newImageUrl.value = response.msg; 
  });
}

/** 获取Redis中的图片路径 #2 */
function getAnotherRedisImage() {
  getAnotherConfigImage().then(response => {
    anotherCurrentImageUrl.value = response.msg;
    anotherNewImageUrl.value = response.msg;
  });
}

/** 提交表单，更新金额配置 */
function handleSubmitAmount() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.loading("正在保存...");
      updateTransferAmount({ amount: form.newAmount }).then(() => {
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("设置成功");
        getCurrentAmount();
        form.newAmount = undefined;
      }).catch(() => {
        proxy.$modal.closeLoading();
      });
    }
  });
}

/** 【改动点】: 移除 !newImageUrl.value 的判断 */
function handleSubmitImage() {
  proxy.$modal.loading("正在保存图片设置...");
  // 如果 newImageUrl 为空字符串，后端会将其视为删除操作
  updateConfigImage(newImageUrl.value).then(() => {
    proxy.$modal.closeLoading();
    proxy.$modal.msgSuccess("图片设置成功");
    getRedisImage();
  }).catch(() => {
    proxy.$modal.closeLoading();
  });
}

/** 【改动点】: 移除 !anotherNewImageUrl.value 的判断 */
function handleSubmitAnotherImage() {
  proxy.$modal.loading("正在保存图片设置...");
  updateAnotherConfigImage(anotherNewImageUrl.value).then(() => {
    proxy.$modal.closeLoading();
    proxy.$modal.msgSuccess("图片设置成功");
    getAnotherRedisImage();
  }).catch(() => {
    proxy.$modal.closeLoading();
  });
}

onMounted(() => {
  getCurrentAmount();
  getRedisImage();
  getAnotherRedisImage();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.setting-body {
  padding: 20px;
  .current-setting {
    display: flex;
    align-items: center;
    font-size: 16px;
    .label { color: #606266; }
    .value { color: #303133; }
    .important { font-weight: bold; font-size: 20px; color: #f56c6c; }
  }
  .el-form { margin-top: 20px; }
}

.current-image-wrapper {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
}
</style>