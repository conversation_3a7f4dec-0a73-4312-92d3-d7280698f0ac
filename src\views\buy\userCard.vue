<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="queryParams.realName" placeholder="请输入真实姓名" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input v-model="queryParams.companyName" placeholder="请输入公司名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="角色" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择角色" clearable>
          <el-option v-for="role in roleOptions" :key="role" :label="role" :value="role"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['buy:userCard:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['buy:userCard:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['buy:userCard:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['buy:userCard:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userCardList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="名片ID" align="center" prop="id" />
      <el-table-column label="关联用户ID" align="center" prop="userId" />
	    <el-table-column label="店铺头像" align="center" prop="avatar" width="100">
	      <template #default="scope"><image-preview :src="scope.row.storeAvatar" :width="50" :height="50"/></template>
	    </el-table-column>
      <el-table-column label="真实姓名" align="center" prop="realName" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="公司名称" align="center" prop="companyName" show-overflow-tooltip />
      <el-table-column label="所在地区" align="center" prop="region" show-overflow-tooltip />
      <el-table-column label="角色" align="center" prop="userType" />
      <el-table-column label="从事行业" align="center" prop="industrys" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:userCard:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:userCard:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="userCardRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="关联的用户" prop="userId">
           <el-select
             v-model="form.userId"
             placeholder="请输入关键字搜索用户"
             filterable
             remote
             :remote-method="remoteUserSearch"
             :loading="userLoading"
             style="width: 100%;"
           >
             <el-option
               v-for="user in userList"
               :key="user.userId"
               :label="`${user.userName} (${user.nickName})`"
               :value="user.userId"
             />
           </el-select>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入店铺或公司名称" />
        </el-form-item>
        <el-form-item label="所在地区" prop="region">
          <el-cascader
            ref="regionCascaderRef"
            :options="addressData"
            v-model="selectedRegion"
            :props="{ value: 'code', label: 'value', children: 'children' }"
            @change="handleRegionChange"
            placeholder="请选择省/市/区"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
		    <el-form-item label="店铺头像" prop="storeAvatar"><image-upload v-model="form.storeAvatar" :limit="1"/></el-form-item>
        <el-form-item label="角色" prop="userType">
          <el-select v-model="form.userType" placeholder="请选择角色" style="width: 100%;">
            <el-option
              v-for="role in roleOptions"
              :key="role"
              :label="role"
              :value="role"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="从事行业" prop="industrys">
          <el-select v-model="form.industrys" multiple placeholder="请选择从事行业" style="width: 100%;">
            <el-option
              v-for="item in industryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserCard">
import { listUserCard, getUserCard, delUserCard, addUserCard, updateUserCard } from "@/api/buy/userCard";
import { listUser, getUser } from "@/api/system/user";
import addressData from "@/utils/china_address_data"


const { proxy } = getCurrentInstance();

const userCardList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const userList = ref([]);
const userLoading = ref(false);
const selectedRegion = ref([]);
const regionCascaderRef = ref(null);
const originalUserId = ref(null);

const roleOptions = ref([
  "工程采购", "零售商店", "单位采购", "生产厂家", "装饰公司", "总代批发", "物流货运","矿山粮库", "其他行业", "个人用户"
]);

const industryOptions = ref([
    { id: 1, name: '五金机电' }, { id: 2, name: '建筑材料' }, { id: 3, name: '建筑配件' },
    { id: 4, name: '消防安防' }, { id: 5, name: '电线电缆' }, { id: 6, name: '电气电料' },
    { id: 7, name: '水暖管件' }, { id: 8, name: '家装装饰' }, { id: 9, name: '灯饰照明' },
    { id: 10, name: '钢材建材' }, { id: 11, name: '工程装饰' }, { id: 12, name: '劳保安防' },
    { id: 13, name: '农资日杂' }, { id: 14, name: '园林石材' }, { id: 15, name: '农机汽配' },
    { id: 16, name: '手动工具' },{ id: 17, name: '电动工具' },{ id: 18, name: '防水保温' },
    { id: 19, name: '螺丝轴承' },{ id: 20, name: '绝缘橡胶' },{ id: 21, name: '五金配件' },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null, realName: null, phone: null, companyName: null,
    region: null, address: null, userType: null, industrys: null,
  },
  rules: {
    realName: [ { required: true, message: "真实姓名不能为空", trigger: "blur" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// === 地区数据处理 Start ===
const nameToCodeMap = new Map();

/**
 * 递归遍历地区数据，构建一个 "名称路径" -> "编码路径" 的映射
 * @param {Array} nodes - 要遍历的节点数组
 * @param {Array} namePath - 父级的名称路径
 * @param {Array} codePath - 父级的编码路径
 */
function buildRegionMap(nodes, namePath = [], codePath = []) {
  if (!Array.isArray(nodes)) return;
  for (const node of nodes) {
    // node.value 是地区名, node.code 是地区编码
    const currentNamePath = [...namePath, node.value];
    const currentCodePath = [...codePath, node.code];
    
    if (node.children && node.children.length > 0) {
      buildRegionMap(node.children, currentNamePath, currentCodePath);
    } else {
      // 到达叶子节点，将完整路径存入Map
      nameToCodeMap.set(currentNamePath.join('-'), currentCodePath);
    }
  }
}

// 组件加载时立即构建Map，供后续使用
buildRegionMap(addressData);
// === 地区数据处理 End ===

/** 查询用户业务名片列表 */
function getList() {
  loading.value = true;
  listUserCard(queryParams.value).then(response => {
    userCardList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 远程搜索用户 */
function remoteUserSearch(query) {
  if (query) {
    userLoading.value = true;
    listUser({ userName: query, pageSize: 50 }).then(response => {
      userList.value = response.rows;
    }).finally(() => {
      userLoading.value = false;
    });
  } else {
    userList.value = [];
  }
}

/** 地区选择变更，将选择结果的文本赋值给form.region */
function handleRegionChange() {
  const checkedNodes = regionCascaderRef.value.getCheckedNodes();
  if (checkedNodes && checkedNodes.length > 0) {
    // pathLabels 是一个包含各级别文本的数组, e.g., ["河北省", "石家庄市", "长安区"]
    form.value.region = checkedNodes[0].pathLabels.join('-');
  } else {
    form.value.region = null;
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null, userId: null, realName: null, phone: null, companyName: null,
    region: null, address: null, userType: null, industrys: [],
    createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null
  };
  selectedRegion.value = [];
  originalUserId.value = null;
  proxy.resetForm("userCardRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  userList.value = [];
  open.value = true;
  title.value = "添加用户业务名片";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value[0];
  
  const response = await getUserCard(_id);
  form.value = response.data;
  originalUserId.value = form.value.userId;

  if (form.value.userId) {
    const userRes = await getUser(form.value.userId);
    userList.value = [userRes.data];
  }

  if (form.value.industrys && typeof form.value.industrys === 'string') {
    form.value.industrys = form.value.industrys.split(',');
  } else {
    form.value.industrys = [];
  }
  
  // 修改点：使用新的 nameToCodeMap 进行地区数据回显
  if (form.value.region) {
    const codes = nameToCodeMap.get(form.value.region);
    if (codes) {
      selectedRegion.value = codes;
    }
  }
  
  open.value = true;
  title.value = "修改用户业务名片";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["userCardRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null && form.value.userId !== originalUserId.value) {
         proxy.$modal.confirm('您正在更改名片的归属用户，这是一个高风险操作，请确认是否继续？', "警告", {
           confirmButtonText: "继续",
           cancelButtonText: "取消",
           type: "warning"
         }).then(() => {
           proceedWithSubmit();
         }).catch(() => {});
       } else {
         proceedWithSubmit();
       }
    }
  });
}

/** 执行提交 */
function proceedWithSubmit() {
  const submitData = { ...form.value };
  if (Array.isArray(submitData.industrys)) {
    submitData.industrys = submitData.industrys.join(',');
  }
  
  if (submitData.id != null) {
    updateUserCard(submitData).then(response => {
      proxy.$modal.msgSuccess("修改成功");
      open.value = false;
      getList();
    });
  } else {
    addUserCard(submitData).then(response => {
      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      getList();
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除用户业务名片编号为"' + _ids + '"的数据项？').then(function() {
    return delUserCard(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('buy/userCard/export', {
    ...queryParams.value
  }, `userCard_${new Date().getTime()}.xlsx`)
}

getList();
</script>