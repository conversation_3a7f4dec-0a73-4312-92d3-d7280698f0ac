import request from '@/utils/request'

// 查询易购圈列表
export function listCircle(query) {
  return request({
    url: '/buy/circle/list',
    method: 'get',
    params: query
  })
}

// 查询易购圈详细
export function getCircle(circleId) {
  return request({
    url: '/buy/circle/' + circleId,
    method: 'get'
  })
}

// 新增易购圈
export function addCircle(data) {
  return request({
    url: '/buy/circle',
    method: 'post',
    data: data
  })
}

// 修改易购圈
export function updateCircle(data) {
  return request({
    url: '/buy/circle',
    method: 'put',
    data: data
  })
}

// 删除易购圈
export function delCircle(circleId) {
  return request({
    url: '/buy/circle/' + circleId,
    method: 'delete'
  })
}
