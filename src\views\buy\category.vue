<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类名称" prop="categoryName">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入分类名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['buy:category:add']"
        >新增分类</el-button>
      </el-col>
       <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Sort"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="categoryList"
      row-key="categoryId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="分类名称" prop="categoryName" :show-overflow-tooltip="true" />
      <el-table-column label="分类ID" align="center" prop="categoryId" />
      <el-table-column label="图片" align="center" prop="imageUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
         <template #default="scope">
           <span>{{ parseTime(scope.row.createTime) }}</span>
         </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['buy:category:edit']">修改</el-button>
          <el-button 
            v-if="isNodeLevelLessThan(scope.row, 3)" 
            link 
            type="primary" 
            icon="Plus" 
            @click="handleAdd(scope.row)" 
            v-hasPermi="['buy:category:add']"
          >新增</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['buy:category:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="categoryRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级分类" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="categoryOptions"
            :props="{ value: 'categoryId', label: 'categoryName', children: 'children' }"
            value-key="categoryId"
            placeholder="请选择上级分类"
            check-strictly
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item v-if="showImageUploader" label="分类图片" prop="imageUrl">
          <image-upload v-model="form.imageUrl"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
           <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Category">
import { listCategory, getCategory, delCategory, addCategory, updateCategory } from "@/api/buy/category";

const { proxy } = getCurrentInstance();

const categoryList = ref([]);
const categoryOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(true);
const refreshTable = ref(true);
const nodesMap = new Map();

const showImageUploader = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    categoryName: null,
	pageSize:1000000
  },
  rules: {
    parentId: [ { required: true, message: "上级分类不能为空", trigger: "change" } ],
    categoryName: [ { required: true, message: "分类名称不能为空", trigger: "blur" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(() => form.value.parentId, (newParentId) => {
  if (newParentId === undefined || newParentId === null) {
    showImageUploader.value = false;
    return;
  }
  
  if (newParentId === 0) {
    showImageUploader.value = false;
  } else {
    const parentNode = nodesMap.get(newParentId);
    if (parentNode && parentNode.parentId !== 0) {
      showImageUploader.value = true;
    } else {
      showImageUploader.value = false;
    }
  }

  if (!showImageUploader.value) {
    form.value.imageUrl = null;
  }
});

/** 查询分类列表 */
function getList() {
  loading.value = true;
  listCategory(queryParams.value).then(response => {
    // 再次请求时，也需要更新 nodesMap
    nodesMap.clear();
    response.rows.forEach(node => nodesMap.set(node.categoryId, node));
    
    categoryList.value = proxy.handleTree(response.rows, "categoryId", "parentId");
    loading.value = false;
  });
}

/** 查询分类下拉树结构 */
function getTreeselect() {
  return listCategory().then(response => {
    nodesMap.clear();
    response.rows.forEach(node => nodesMap.set(node.categoryId, node));

    categoryOptions.value = [];
    const data = { categoryId: 0, categoryName: '顶级分类', children: [] };
    data.children = proxy.handleTree(response.rows, "categoryId", "parentId");
    categoryOptions.value.push(data);
  });
}

/** 【修复点】: 新增一个辅助函数用于判断节点层级 */
function isNodeLevelLessThan(node, level) {
  if (!node || node.parentId === undefined) return false;
  
  let currentNode = node;
  let currentLevel = 1;
  
  while (currentNode.parentId !== 0) {
    currentNode = nodesMap.get(currentNode.parentId);
    if (!currentNode) return false; // 如果找不到父节点，则退出
    currentLevel++;
  }
  
  return currentLevel < level;
}


// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    categoryId: null,
    parentId: 0, 
    ancestors: null,
    imageUrl: null,
    categoryName: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  showImageUploader.value = false;
  proxy.resetForm("categoryRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function handleAdd(row) {
  reset();
  await getTreeselect();

  if (row != null && row.categoryId) {
    form.value.parentId = row.categoryId;
  } else {
    form.value.parentId = 0;
  }
  open.value = true;
  title.value = "添加分类";
}

async function handleUpdate(row) {
  reset();
  const categoryId = row.categoryId;
  
  await getTreeselect();
  
  getCategory(categoryId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改分类";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["categoryRef"].validate(valid => {
    if (valid) {
      if (form.value.categoryId != null) {
        updateCategory(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCategory(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (row.children && row.children.length > 0) {
     proxy.$modal.msgWarning("存在子节点,不允许删除");
     return;
  }
  const categoryIds = row.categoryId;
  proxy.$modal.confirm('是否确认删除分类编号为"' + categoryIds + '"的数据项？').then(function() {
    return delCategory(categoryIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

getList();
</script>