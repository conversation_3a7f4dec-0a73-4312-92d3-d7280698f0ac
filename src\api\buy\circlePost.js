import request from '@/utils/request'

// 查询易购圈帖子列表
export function listCirclePost(query) {
  return request({
    url: '/buy/circlePost/list',
    method: 'get',
    params: query
  })
}

// 查询易购圈帖子详细
export function getCirclePost(postId) {
  return request({
    url: '/buy/circlePost/' + postId,
    method: 'get'
  })
}

// 新增易购圈帖子
export function addCirclePost(data) {
  return request({
    url: '/buy/circlePost',
    method: 'post',
    data: data
  })
}

// 修改易购圈帖子
export function updateCirclePost(data) {
  return request({
    url: '/buy/circlePost',
    method: 'put',
    data: data
  })
}

// 删除易购圈帖子
export function delCirclePost(postId) {
  return request({
    url: '/buy/circlePost/' + postId,
    method: 'delete'
  })
}
