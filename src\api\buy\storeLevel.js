import request from '@/utils/request'

// 查询店铺等级列表
export function listStoreLevel(query) {
  return request({
    url: '/buy/storeLevel/list',
    method: 'get',
    params: query
  })
}

// 查询店铺等级详细
export function getStoreLevel(id) {
  return request({
    url: '/buy/storeLevel/' + id,
    method: 'get'
  })
}

// 新增店铺等级
export function addStoreLevel(data) {
  return request({
    url: '/buy/storeLevel',
    method: 'post',
    data: data
  })
}

// 修改店铺等级
export function updateStoreLevel(data) {
  return request({
    url: '/buy/storeLevel',
    method: 'put',
    data: data
  })
}

// 删除店铺等级
export function delStoreLevel(id) {
  return request({
    url: '/buy/storeLevel/' + id,
    method: 'delete'
  })
}
