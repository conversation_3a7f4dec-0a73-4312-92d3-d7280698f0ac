import request from '@/utils/request'

// 查询商家退款列表
export function listStoreRefund(query) {
  return request({
    url: '/buy/storeRefund/list',
    method: 'get',
    params: query
  })
}

// 查询商家退款详细
export function getStoreRefund(refundId) {
  return request({
    url: '/buy/storeRefund/' + refundId,
    method: 'get'
  })
}

// 新增商家退款
export function addStoreRefund(data) {
  return request({
    url: '/buy/storeRefund',
    method: 'post',
    data: data
  })
}

// 修改商家退款
export function updateStoreRefund(data) {
  return request({
    url: '/buy/storeRefund',
    method: 'put',
    data: data
  })
}

// 删除商家退款
export function delStoreRefund(refundId) {
  return request({
    url: '/buy/storeRefund/' + refundId,
    method: 'delete'
  })
}
