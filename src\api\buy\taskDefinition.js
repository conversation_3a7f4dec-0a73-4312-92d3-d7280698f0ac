import request from '@/utils/request'

// 查询任务定义列表
export function listTaskDefinition(query) {
  return request({
    url: '/buy/taskDefinition/list',
    method: 'get',
    params: query
  })
}

// 查询任务定义详细
export function getTaskDefinition(taskId) {
  return request({
    url: '/buy/taskDefinition/' + taskId,
    method: 'get'
  })
}

// 新增任务定义
export function addTaskDefinition(data) {
  return request({
    url: '/buy/taskDefinition',
    method: 'post',
    data: data
  })
}

// 修改任务定义
export function updateTaskDefinition(data) {
  return request({
    url: '/buy/taskDefinition',
    method: 'put',
    data: data
  })
}

// 删除任务定义
export function delTaskDefinition(taskId) {
  return request({
    url: '/buy/taskDefinition/' + taskId,
    method: 'delete'
  })
}
